import { Injectable } from "bwtk";
import { Epic, combineEpics , ofType } from "redux-observable";
import { EWidgetStatus, Actions, Models, AjaxResponse, ValueOf, EWidgetName, EWidgetRoute } from "omf-changepackage-components";
import { Client } from "../../Client";
import {
  IAppointmentAPIResponse, MapRequestData, Request
} from "../../models";
import {
  getAppointment,
  setAvailableDates,
  // setPreferredDate,
  contactInformation,
  setDuration,
  setInstallationAddress,
  setAdditionalDetails,
  setIsInstallationRequired,
  setAppointment
} from "../Actions";
import { Config } from "../../Config";
import { filter, mergeMap, catchError , of, concat, from } from "rxjs";



// Actions destructuring
const {
  errorOccured,
  setWidgetStatus,
  setProductConfigurationTotal,
  broadcastUpdate,
  historyGo,
  clearCachedState,
  omniPageLoaded
} = Actions;

@Injectable
export class AppointmentEpics {
  widgetState: EWidgetStatus = EWidgetStatus.INIT;

  constructor(private client: Client, private config: Config) { }

  combineEpics() {
    return combineEpics(
      this.appointmentEpic,
      this.submitAppointmentEpic
    );
  }

  private get appointmentEpic(): AppointmentEpic {
    return (action$: any) =>
      action$.pipe(
        ofType(getAppointment.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(() => 
          concat(
            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
            this.client.get<AjaxResponse<IAppointmentAPIResponse>>(this.config.api.appointmentAPI).pipe(
              mergeMap(({ data }) => of(
                setAvailableDates(data.appointment.availableDates),
                // setPreferredDate(data.appointment.preferredDate),
                setDuration(data.appointment.duration),
                setInstallationAddress(data.appointment.installationAddress),
                contactInformation(data.appointment.contactInformation),
                setAdditionalDetails(data.appointment.additionalDetails),
                setIsInstallationRequired(data.appointment.isInstallationRequired),
                broadcastUpdate(setProductConfigurationTotal(ValueOf(data, "productConfigurationTotal"))),
                setWidgetStatus(this.widgetState = EWidgetStatus.RENDERED),
                omniPageLoaded()
              ))
            )
          )
        ),
        catchError((error: Response) => of(
          errorOccured(new Models.ErrorHandler("getAppointment", error))
        ))
      );
  }

  private get submitAppointmentEpic(): AppointmentEpic {
    return (action$: any, store: any) =>
      action$.pipe(
        ofType(setAppointment.toString()),
        filter(() => this.widgetState !== EWidgetStatus.UPDATING),
        mergeMap(({ payload }: any) => 
          concat(
            of(setWidgetStatus(this.widgetState = EWidgetStatus.UPDATING)),
            this.client.put<any>(this.config.api.appointmentAPI, MapRequestData.create(payload, Request, store.getState())).pipe(
              mergeMap(() => from([
                broadcastUpdate(historyGo(EWidgetRoute.REVIEW)),
                clearCachedState([EWidgetName.REVIEW])
              ]))
            )
          )
        ),
        catchError((error: Response) => of(
          errorOccured(new Models.ErrorHandler("getAppointment", error))
        ))
      );
  }
}

type AppointmentEpic = Epic<any, any, void, any>;
